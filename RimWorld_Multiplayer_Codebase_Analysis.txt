================================================================================
                    RIMWORLD MULTIPLAYER MOD - CODEBASE ANALYSIS REPORT
================================================================================

OVERVIEW
--------
The RimWorld Multiplayer mod is a complex C# project that enables multiplayer 
functionality for the RimWorld game. The codebase is organized into five main 
modules: Client, Server, Common, MultiplayerLoader, and Tests. The architecture 
follows a client-server model with sophisticated synchronization mechanisms.

PROJECT STRUCTURE
-----------------
- Package ID: rwmt.Multiplayer.TTDG
- Supported RimWorld Versions: 1.5, 1.6
- Dependencies: Harmony (brrainz.harmony)
- Language: C# (.NET)
- Solution: Visual Studio solution with 6 projects

================================================================================
                                CLIENT MODULE
================================================================================

PURPOSE
-------
The Client module handles all client-side multiplayer functionality including 
networking, UI, synchronization, patches to RimWorld's core systems, and 
session management.

MAIN COMPONENTS
---------------

1. MULTIPLAYER CORE (Multiplayer.cs)
   Public Properties:
   - harmony: Harmony instance for patching
   - settings: MpSettings for configuration
   - game: MultiplayerGame instance
   - session: MultiplayerSession instance
   - serialization: SyncSerialization instance
   - LocalServer: MultiplayerServer for local hosting
   - Client: ConnectionBase property (session?.client)
   - username: string for player identification

2. MULTIPLAYER GAME (MultiplayerGame.cs)
   Public Properties:
   - sync: SyncCoordinator for synchronization
   - asyncWorldTimeComp: AsyncWorldTimeComp for time management
   - worldComp: MultiplayerWorldComp for world-level data
   - gameComp: MultiplayerGameComp for game-level data
   - mapComps: List<MultiplayerMapComp> for map-level data
   - asyncTimeComps: List<AsyncTimeComp> for async time handling
   - sharedCrossRefs: SharedCrossRefs for cross-references
   - playerDebugState: Dictionary<int, PlayerDebugState>
   - RealPlayerFaction: Faction property

3. NETWORKING SUBSYSTEM
   Components:
   - ClientUtil: Static utility class for connection management
     * TryConnectWithWindow(address, port, returnToServerBrowser): void
     * TrySteamConnectWithWindow(user, returnToServerBrowser): void
   
   - LocalClientConnection: In-memory connection for local servers
     * Latency: int property (always 0)
     * SendRaw(raw, reliable): protected override method
     * Close(reason, data): override method
   
   - SteamBaseConn: Abstract base for Steam P2P connections
     * remoteId: CSteamID readonly field
     * SendRaw(raw, reliable): protected override method
     * OnError(error): abstract method

4. SYNCHRONIZATION SYSTEM
   Components:
   - SyncUtil: Static utility class for sync operations
     * isDialogNodeTreeOpen: bool static field
     * PatchMethodForSync(method): internal static method
     * PatchMethodForDialogNodeTreeSync(method): internal static method
   
   - SyncGame: Static initialization class
     * Init(): static method - initializes sync subsystems
   
   - SyncDict: Static dictionary management
     * syncWorkers: SyncWorkerDictionaryTree internal field
     * Init(): static method

5. USER INTERFACE
   Components:
   - IngameUIPatch: HarmonyPatch for main UI
     * upperLeftDrawers: List<Func<float, float>> static field
     * tps: float static field for TPS display
   
   - MainMenuPatch: HarmonyPatch for main menu modifications
     * Adds multiplayer button to main menu
   
   - MpUI: Static utility class for UI helpers
     * ButtonTextWithTip(rect, label, tip, disabled, tipId): bool static method

6. PATCHES SYSTEM
   Components:
   - LongEvents patches: Handle long-running operations
     * MarkLongEvents: Prefix patch for QueueLongEvent
     * LongEventEnd: Postfix patch for ExecuteToExecuteWhenFinished
   
   - CaravanSplittingPatches: Handle caravan splitting in multiplayer
     * CancelDialogSplitCaravan: Prefix patch for WindowStack.Add

DEPENDENCIES
------------
High Frequency:
- Common module (networking, serialization, server communication)
- RimWorld core assemblies (Verse, RimWorld, UnityEngine)
- Harmony library (for runtime patching)

Medium Frequency:
- Multiplayer.API (for mod compatibility)
- LiteNetLib (for networking)
- Steamworks.NET (for Steam integration)

Low Frequency:
- System libraries (Threading, Collections, IO)

================================================================================
                                SERVER MODULE
================================================================================

PURPOSE
-------
The Server module provides dedicated server functionality, handling multiple 
client connections, world state management, command processing, and game 
synchronization across all connected players.

MAIN COMPONENTS
---------------

1. MULTIPLAYER SERVER (MultiplayerServer.cs)
   Public Properties:
   - instance: static MultiplayerServer singleton
   - worldData: WorldData for game state
   - freezeManager: FreezeManager for pause handling
   - commands: CommandHandler for command processing
   - playerManager: PlayerManager for player management
   - liteNet: LiteNetManager for network management
   - JoinedPlayers: IEnumerable<ServerPlayer> property
   - PlayingPlayers: IEnumerable<ServerPlayer> property
   - settings: ServerSettings configuration
   - running: volatile bool for server state
   - gameTimer: int for game timing
   - NetTimer: int property for network timing
   
   Public Methods:
   - MultiplayerServer(settings): constructor
   - TryStop(): void - graceful shutdown
   - Enqueue(action): void - thread-safe action queuing
   - SendToPlaying(id, data, reliable, excluding): void - broadcast to players
   - GetPlayer(username/id): ServerPlayer? - player lookup

2. PLAYER MANAGER (PlayerManager.cs)
   Public Properties:
   - Players: List<ServerPlayer> for all connected players
   - JoinedPlayers: IEnumerable<ServerPlayer> property
   - PlayingPlayers: IEnumerable<ServerPlayer> property
   
   Public Methods:
   - OnJoin(player): void - handle player joining
   - SendInitDataCommand(player): void - send initial data
   - OnServerStop(): void - cleanup on shutdown
   - MakeHost(host): void - designate host player
   - GetPlayer(username/id): ServerPlayer? - player lookup

3. WORLD DATA (WorldData.cs)
   Public Properties:
   - hostFactionId: int for host faction
   - spectatorFactionId: int for spectator faction
   - savedGame: byte[] compressed save data
   - sessionData: byte[] compressed session data
   - mapData: Dictionary<int, byte[]> for map-specific data
   - mapCmds: Dictionary<int, List<byte[]>> for map commands
   - syncInfos: List<byte[]> for synchronization data
   - CreatingJoinPoint: bool property
   - Server: MultiplayerServer property

4. COMMAND HANDLER (CommandHandler.cs)
   Public Properties:
   - SentCmds: int for command tracking
   
   Public Methods:
   - Send(cmd, factionId, mapId, data, sourcePlayer, fauxSource): void
   - CanUseDevMode(player): bool - permission checking

DEPENDENCIES
------------
High Frequency:
- Common module (shared networking, data structures)
- LiteNetLib (network communication)

Medium Frequency:
- System libraries (Threading, Collections, IO)

Low Frequency:
- Compression libraries (for save data)

================================================================================
                                COMMON MODULE
================================================================================

PURPOSE
-------
The Common module contains shared code used by both Client and Server modules,
including networking protocols, serialization systems, data structures, and
utility classes.

MAIN COMPONENTS
---------------

1. NETWORKING FOUNDATION
   Components:
   - ConnectionBase: Abstract base class for all connections
     * username: string property
     * serverPlayer: ServerPlayer property
     * Latency: virtual int property
     * State: ConnectionStateEnum property
     * StateObj: MpConnectionState property
     * ChangeState(state): void method
     * Send(id, data, reliable): void method
     * SendFragmented(id, data): void method
     * HandleReceiveRaw(data, reliable): virtual void method
   
   - LiteNetConnection: Concrete implementation for LiteNetLib
     * peer: NetPeer readonly field
     * SendRaw(raw, reliable): protected override method
     * Close(reason, data): override method
   
   - Packets: Enum defining all packet types
     * Client_* packets: originated from client
     * Server_* packets: originated from server
     * Special packets: for protocol negotiation

2. SERIALIZATION SYSTEM
   Components:
   - SyncSerialization: Core serialization engine
     * TypeHelper: SyncTypeHelper property
     * syncTree: SyncWorkerDictionaryTree field
     * ReadSync<T>(data): T method
     * WriteSync<T>(data, obj): void method
     * ReadSyncObject(data, type): object method
     * WriteSyncObject(data, obj, type): void method
   
   - ByteWriter: Binary data writing
     * WriteInt32(val): virtual void method
     * WriteBool(val): virtual void method
     * WriteFloat(val): virtual void method
     * WritePrefixedBytes(bytes): virtual void method
     * GetBytes(data): static byte[] method
   
   - ByteReader: Binary data reading
     * ReadInt32(): virtual int method
     * ReadBool(): virtual bool method
     * ReadFloat(): virtual float method
     * ReadPrefixedBytes(): virtual byte[] method

3. SERVER INFRASTRUCTURE
   Components:
   - ServerPlayer: Represents connected player
     * id: int field
     * conn: ConnectionBase field
     * type: PlayerType field
     * status: PlayerStatus field
     * color: ColorRGB field
     * hasJoined: bool field
     * Username: string property
     * IsHost: bool property
     * IsArbiter: bool property
   
   - ServerInitData: Server initialization data record
     * RawData: byte[] field
     * RwVersion: string field
     * DebugOnlySyncCmds: HashSet<int> field
     * HostOnlySyncCmds: HashSet<int> field
     * Serialize(data): static byte[] method

4. UTILITY CLASSES
   Components:
   - ActionQueue: Thread-safe action queuing
     * RunQueue(errorLogger): void method
     * Enqueue(action): void method
   
   - FreezeManager: Game pause management
     * Frozen: bool property
     * Server: MultiplayerServer property
     * Tick(): void method

DEPENDENCIES
------------
High Frequency:
- System libraries (Collections, Threading, IO)
- LiteNetLib (networking)

Medium Frequency:
- Compression libraries
- Serialization frameworks

================================================================================
                            MULTIPLAYERLOADER MODULE
================================================================================

PURPOSE
-------
The MultiplayerLoader module serves as the entry point and bootstrap for the
multiplayer mod, handling assembly loading and initial setup.

MAIN COMPONENTS
---------------

1. MULTIPLAYER LOADER (MultiplayerLoader.cs)
   Public Properties:
   - instance: static Multiplayer singleton
   - settingsWindowDrawer: static Action<Rect>
   
   Public Methods:
   - Multiplayer(content): constructor - ModContentPack parameter
   - LoadAssembliesCustom(): private void method

DEPENDENCIES
------------
High Frequency:
- RimWorld mod system (Verse.Mod)
- Reflection APIs

Medium Frequency:
- Assembly loading mechanisms

================================================================================
                                INTERACTION ANALYSIS
================================================================================

HIGH FREQUENCY INTERACTIONS
---------------------------
1. Client ↔ Common: Continuous networking and serialization
2. Server ↔ Common: Player management and command processing  
3. Client ↔ Server: Real-time game synchronization via Common protocols
4. Client patches ↔ RimWorld core: Runtime behavior modification

MEDIUM FREQUENCY INTERACTIONS
-----------------------------
1. MultiplayerLoader → Client: Initialization and setup
2. Client UI ↔ Client networking: User-initiated network operations
3. Server components ↔ WorldData: Game state persistence
4. Serialization ↔ Game objects: Save/load operations

LOW FREQUENCY INTERACTIONS
--------------------------
1. Tests ↔ All modules: Validation and testing
2. Server ↔ File system: Configuration and save management
3. Client ↔ Steam API: Steam integration features

CRITICAL DEPENDENCIES
---------------------
1. Harmony: Essential for runtime patching of RimWorld
2. LiteNetLib: Core networking infrastructure
3. RimWorld assemblies: Game integration foundation
4. .NET Framework: Runtime environment

================================================================================
                                    SUMMARY
================================================================================

The RimWorld Multiplayer mod is a sophisticated system with clear separation of
concerns across its modules. The Client handles user interaction and game
integration, the Server manages multiplayer sessions, the Common module provides
shared infrastructure, and MultiplayerLoader bootstraps the system. The
architecture supports both local and networked multiplayer with robust
synchronization and state management capabilities.

Key strengths include modular design, comprehensive networking layer, and
extensive integration with RimWorld's systems through Harmony patching.
